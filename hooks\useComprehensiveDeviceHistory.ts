import React, { useMemo } from 'react';
import { 
  Package, Wrench, ShoppingCart, Undo2, Clip<PERSON><PERSON><PERSON><PERSON>, 
  Shuffle, PackageCheck, Replace, FileText, AlertCircle,
  Truck, UserCheck, Settings, CheckCircle
} from 'lucide-react';

interface ComprehensiveTimelineEvent {
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  date: string;
  color: string;
  user?: string;
  type: string;
  details: any;
  priority: number; // للترتيب حسب الأهمية
}

interface UseComprehensiveDeviceHistoryProps {
  deviceId: string;
  allData: {
    devices: any[];
    sales: any[];
    returns: any[];
    supplyOrders: any[];
    suppliers: any[];
    evaluationOrders: any[];
    maintenanceHistory: any[];
    maintenanceOrders: any[];
    maintenanceReceiptOrders: any[];
    maintenanceLogs: any[];
    maintenanceReceipts: any[];
    warehouseTransfers: any[];
    deliveryOrders: any[];
  };
}

export function useComprehensiveDeviceHistory({ 
  deviceId, 
  allData 
}: UseComprehensiveDeviceHistoryProps): ComprehensiveTimelineEvent[] {
  
  return useMemo(() => {
    if (!deviceId || !allData) return [];

    const events: ComprehensiveTimelineEvent[] = [];
    const device = allData.devices?.find((d: any) => d.id === deviceId);

    // 1. توريد الجهاز - Supply Events
    const relatedSupplyOrders = (allData.supplyOrders || []).filter((order: any) => {
      if (!order.items) return false;
      const items = Array.isArray(order.items) ? order.items : 
                   typeof order.items === 'string' ? JSON.parse(order.items) : [];
      return items.some((item: any) => item.imei === deviceId || item.deviceId === deviceId);
    });

    relatedSupplyOrders.forEach((order: any) => {
      const supplier = allData.suppliers?.find((s: any) => s.id === order.supplierId);
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.imei === deviceId || item.deviceId === deviceId);

      events.push({
        id: `supply-${order.id}-${deviceId}`,
        icon: <Package className="h-5 w-5" />,
        title: 'توريد الجهاز',
        description: `تم استلام الجهاز من المورد "${supplier?.name || 'غير معروف'}" ضمن أمر التوريد ${order.supplyOrderId || order.orderNumber}. ${deviceItem?.purchasePrice ? `سعر الشراء: ${deviceItem.purchasePrice}` : ''}`,
        date: order.supplyDate || order.date || order.createdAt,
        color: 'bg-cyan-500/20 text-cyan-600',
        user: order.employeeName,
        type: 'supply',
        priority: 1,
        details: {
          supplyOrderId: order.supplyOrderId || order.orderNumber,
          supplierName: supplier?.name,
          supplierCode: supplier?.code,
          purchasePrice: deviceItem?.purchasePrice,
          warehouseName: order.warehouseName,
          invoiceNumber: order.invoiceNumber,
          model: deviceItem?.model,
          condition: deviceItem?.condition,
          notes: order.notes,
          attachments: order.attachmentName
        }
      });
    });

    // 2. فحص وتقييم - Evaluation Events
    const relatedEvaluations = (allData.evaluationOrders || []).filter((order: any) => {
      if (!order.items) return false;
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      return items.some((item: any) => item.deviceId === deviceId);
    });

    relatedEvaluations.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === deviceId);

      if (deviceItem) {
        const gradeColor = deviceItem.finalGrade === 'A' ? 'bg-green-500/20 text-green-600' :
                          deviceItem.finalGrade === 'B' ? 'bg-blue-500/20 text-blue-600' :
                          deviceItem.finalGrade === 'C' ? 'bg-yellow-500/20 text-yellow-600' :
                          deviceItem.finalGrade === 'D' ? 'bg-orange-500/20 text-orange-600' :
                          'bg-red-500/20 text-red-600';

        const gradeDetails = [
          `خارجي: ${deviceItem.externalGrade || 'غير محدد'}`,
          `شاشة: ${deviceItem.screenGrade || 'غير محدد'}`,
          `شبكة: ${deviceItem.networkGrade || 'غير محدد'}`
        ].join(' | ');

        events.push({
          id: `evaluation-${order.id}-${deviceId}`,
          icon: <ClipboardCheck className="h-5 w-5" />,
          title: 'فحص وتقييم شامل',
          description: `تم فحص وتقييم الجهاز بدقة. التقييم النهائي: ${deviceItem.finalGrade || 'غير محدد'}. التفاصيل: ${gradeDetails}. ${deviceItem.fault ? `مشاكل: ${deviceItem.fault}` : ''} ${deviceItem.expectedPrice ? `السعر المتوقع: ${deviceItem.expectedPrice}` : ''}`,
          date: order.date || order.createdAt,
          color: gradeColor,
          user: order.employeeName,
          type: 'evaluation',
          priority: 2,
          details: {
            evaluationOrderId: order.orderId,
            finalGrade: deviceItem.finalGrade,
            externalGrade: deviceItem.externalGrade,
            screenGrade: deviceItem.screenGrade,
            networkGrade: deviceItem.networkGrade,
            batteryHealth: deviceItem.batteryHealth,
            cameraQuality: deviceItem.cameraQuality,
            soundQuality: deviceItem.soundQuality,
            touchSensitivity: deviceItem.touchSensitivity,
            fault: deviceItem.fault,
            damageType: deviceItem.damageType,
            condition: deviceItem.condition,
            expectedPrice: deviceItem.expectedPrice,
            marketValue: deviceItem.marketValue,
            testResults: deviceItem.testResults,
            recommendations: deviceItem.recommendations,
            needsMaintenance: deviceItem.needsMaintenance,
            notes: order.notes,
            attachments: order.attachmentName,
            warehouseName: order.warehouseName,
            acknowledgedBy: order.acknowledgedBy,
            status: order.status
          }
        });
      }
    });

    // 3. إرسال للصيانة - Maintenance Send Events
    const relatedMaintenanceOrders = (allData.maintenanceOrders || []).filter((order: any) => {
      if (!order.items) return false;
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      return items.some((item: any) => item.deviceId === deviceId);
    });

    relatedMaintenanceOrders.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === deviceId);

      if (deviceItem) {
        const source = order.source === 'direct' ? 'استلام مباشر في الصيانة' : 'إرسال للصيانة';
        
        events.push({
          id: `maintenance-send-${order.id}-${deviceId}`,
          icon: <Wrench className="h-5 w-5" />,
          title: source,
          description: `${source} ضمن أمر ${order.orderNumber}. ${deviceItem.fault || deviceItem.issueDescription ? `المشكلة: ${deviceItem.fault || deviceItem.issueDescription}` : ''} ${deviceItem.expectedCost ? `التكلفة المتوقعة: ${deviceItem.expectedCost}` : ''}`,
          date: order.date || order.createdAt,
          color: 'bg-orange-500/20 text-orange-600',
          user: order.employeeName,
          type: 'maintenance-send',
          priority: 3,
          details: {
            orderNumber: order.orderNumber,
            referenceNumber: order.referenceNumber,
            fault: deviceItem.fault,
            issueDescription: deviceItem.issueDescription,
            damageType: deviceItem.damageType,
            priority: deviceItem.priority,
            expectedCost: deviceItem.expectedCost,
            maintenanceEmployee: order.maintenanceEmployeeName,
            source: order.source,
            status: order.status,
            notes: deviceItem.notes || order.notes,
            attachments: order.attachmentName
          }
        });
      }
    });

    // 4. استلام من الصيانة - Maintenance Receipt Events  
    const relatedMaintenanceReceipts = (allData.maintenanceReceiptOrders || allData.maintenanceReceipts || []).filter((order: any) => {
      if (!order.items) return false;
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      return items.some((item: any) => item.deviceId === deviceId);
    });

    relatedMaintenanceReceipts.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === deviceId);

      if (deviceItem) {
        const resultText = deviceItem.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                          deviceItem.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل فنياً' :
                          deviceItem.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف فيزيائياً' :
                          deviceItem.result || 'نتيجة غير محددة';

        const receiptColor = deviceItem.result === 'Repaired' ? 'bg-green-500/20 text-green-600' : 'bg-red-500/20 text-red-600';

        events.push({
          id: `maintenance-receipt-${order.id}-${deviceId}`,
          icon: <PackageCheck className="h-5 w-5" />,
          title: 'استلام من الصيانة',
          description: `تم استلام الجهاز من قسم الصيانة ضمن أمر ${order.receiptNumber}. النتيجة: ${resultText}. ${deviceItem.repairCost ? `تكلفة الإصلاح: ${deviceItem.repairCost}` : ''} ${deviceItem.repairDuration ? `مدة الإصلاح: ${deviceItem.repairDuration}` : ''}`,
          date: order.date || order.createdAt,
          color: receiptColor,
          user: order.employeeName,
          type: 'maintenance-receipt',
          priority: 4,
          details: {
            receiptNumber: order.receiptNumber,
            referenceNumber: order.referenceNumber,
            result: deviceItem.result,
            resultText: resultText,
            fault: deviceItem.fault,
            damage: deviceItem.damage,
            repairDescription: deviceItem.repairDescription,
            repairCost: deviceItem.repairCost,
            repairDuration: deviceItem.repairDuration,
            partsUsed: deviceItem.partsUsed,
            maintenanceEmployee: order.maintenanceEmployeeName,
            warehouseName: order.warehouseName,
            notes: deviceItem.notes || order.notes,
            attachments: order.attachmentName,
            status: order.status
          }
        });
      }
    });

    // 5. سجلات الصيانة - Maintenance Logs
    const relatedMaintenanceLogs = (allData.maintenanceLogs || []).filter((log: any) => 
      log.deviceId === deviceId
    );

    relatedMaintenanceLogs.forEach((log: any) => {
      const resultText = log.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                        log.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل' :
                        log.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف' :
                        log.result || 'في انتظار النتيجة';

      const statusText = log.status === 'pending' ? 'قيد العمل' :
                        log.status === 'acknowledged' ? 'مكتمل ومستلم' :
                        log.status || 'غير محدد';

      events.push({
        id: `maintenance-log-${log.id}-${deviceId}`,
        icon: <Settings className="h-5 w-5" />,
        title: `سجل صيانة - ${statusText}`,
        description: `${log.notes || 'سجل صيانة للجهاز'}. النتيجة: ${resultText}`,
        date: log.repairDate || log.createdAt,
        color: log.result === 'Repaired' ? 'bg-green-500/20 text-green-600' : 
              log.status === 'pending' ? 'bg-orange-500/20 text-orange-600' :
              'bg-gray-500/20 text-gray-600',
        user: log.acknowledgedBy || 'فني الصيانة',
        type: 'maintenance-log',
        priority: 4,
        details: {
          model: log.model,
          result: log.result,
          resultText: resultText,
          status: log.status,
          statusText: statusText,
          notes: log.notes,
          warehouseName: log.warehouseName,
          acknowledgedBy: log.acknowledgedBy,
          acknowledgedDate: log.acknowledgedDate,
          repairDate: log.repairDate
        }
      });

      // إضافة حدث منفصل للاستلام
      if (log.status === 'acknowledged' && log.acknowledgedDate) {
        events.push({
          id: `maintenance-acknowledged-${log.id}-${deviceId}`,
          icon: <CheckCircle className="h-5 w-5" />,
          title: 'استلام من الصيانة في المخزن',
          description: `تم استلام الجهاز في المخزن بعد انتهاء الصيانة. ${log.warehouseName ? `المخزن: ${log.warehouseName}` : ''}`,
          date: log.acknowledgedDate,
          color: 'bg-purple-500/20 text-purple-600',
          user: log.acknowledgedBy || 'موظف المخزن',
          type: 'warehouse-receipt',
          priority: 5,
          details: {
            acknowledgedBy: log.acknowledgedBy,
            acknowledgedDate: log.acknowledgedDate,
            warehouseName: log.warehouseName,
            originalResult: log.result,
            repairDate: log.repairDate
          }
        });
      }
    });

    // 6. التحويلات المخزنية - Warehouse Transfers
    const relatedTransfers = (allData.warehouseTransfers || []).filter((transfer: any) => {
      if (!transfer.items) return false;
      const items = Array.isArray(transfer.items) ? transfer.items : JSON.parse(transfer.items || '[]');
      return items.some((item: any) => item.deviceId === deviceId);
    });

    relatedTransfers.forEach((transfer: any) => {
      const items = Array.isArray(transfer.items) ? transfer.items : JSON.parse(transfer.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === deviceId);

      if (deviceItem) {
        const statusText = transfer.status === 'completed' ? 'مكتمل' : 
                          transfer.status === 'pending' ? 'بانتظار الاستلام' :
                          transfer.status === 'cancelled' ? 'ملغي' : 'معلق';

        events.push({
          id: `transfer-${transfer.id}-${deviceId}`,
          icon: <Shuffle className="h-5 w-5" />,
          title: `نقل مخزني (${statusText})`,
          description: `تم نقل الجهاز من مخزن "${transfer.fromWarehouseName}" إلى مخزن "${transfer.toWarehouseName}". أمر التحويل: ${transfer.transferNumber}. ${deviceItem.reason ? `السبب: ${deviceItem.reason}` : ''}`,
          date: transfer.date || transfer.createdAt,
          color: transfer.status === 'completed' ? 'bg-green-500/20 text-green-600' : 
                transfer.status === 'pending' ? 'bg-yellow-500/20 text-yellow-600' :
                'bg-gray-500/20 text-gray-600',
          user: transfer.employeeName,
          type: 'warehouse-transfer',
          priority: 6,
          details: {
            transferNumber: transfer.transferNumber,
            fromWarehouse: transfer.fromWarehouseName,
            toWarehouse: transfer.toWarehouseName,
            status: transfer.status,
            reason: deviceItem.reason,
            urgency: transfer.urgency,
            notes: transfer.notes,
            requestedBy: transfer.requestedBy,
            approvedBy: transfer.approvedBy,
            completedDate: transfer.completedDate,
            attachments: transfer.attachmentName
          }
        });

        // إضافة حدث الاستلام إذا اكتمل
        if (transfer.status === 'completed' && transfer.completedDate) {
          events.push({
            id: `transfer-completed-${transfer.id}-${deviceId}`,
            icon: <PackageCheck className="h-5 w-5" />,
            title: 'استلام في المخزن الجديد',
            description: `تم استلام الجهاز في مخزن "${transfer.toWarehouseName}" بنجاح`,
            date: transfer.completedDate,
            color: 'bg-green-500/20 text-green-600',
            user: transfer.receivedBy || 'موظف المخزن',
            type: 'warehouse-receipt',
            priority: 7,
            details: {
              transferNumber: transfer.transferNumber,
              finalWarehouse: transfer.toWarehouseName,
              receivedBy: transfer.receivedBy,
              completedDate: transfer.completedDate,
              originalTransferDate: transfer.date
            }
          });
        }
      }
    });

    // 7. أوامر التسليم - Delivery Orders
    const relatedDeliveryOrders = (allData.deliveryOrders || []).filter((order: any) => {
      if (!order.items) return false;
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      return items.some((item: any) => item.deviceId === deviceId);
    });

    relatedDeliveryOrders.forEach((order: any) => {
      events.push({
        id: `delivery-${order.id}-${deviceId}`,
        icon: <Truck className="h-5 w-5" />,
        title: 'أمر تسليم',
        description: `تم إنشاء أمر تسليم رقم ${order.deliveryOrderNumber} للجهاز. ${order.notes ? `ملاحظات: ${order.notes}` : ''}`,
        date: order.date || order.createdAt,
        color: 'bg-indigo-500/20 text-indigo-600',
        user: order.employeeName,
        type: 'delivery',
        priority: 8,
        details: {
          deliveryOrderNumber: order.deliveryOrderNumber,
          referenceNumber: order.referenceNumber,
          warehouseName: order.warehouseName,
          status: order.status,
          notes: order.notes,
          attachments: order.attachmentName
        }
      });
    });

    // 8. البيع - Sales Events
    const relatedSales = (allData.sales || []).filter((sale: any) => {
      if (!sale.items) return false;
      const items = Array.isArray(sale.items) ? sale.items : JSON.parse(sale.items || '[]');
      return items.some((item: any) => item.deviceId === deviceId);
    });

    relatedSales.forEach((sale: any) => {
      const items = Array.isArray(sale.items) ? sale.items : JSON.parse(sale.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === deviceId);

      if (deviceItem) {
        const warrantyText = sale.warrantyPeriod === '3d' ? '3 أيام' :
                            sale.warrantyPeriod === '1w' ? 'أسبوع' :
                            sale.warrantyPeriod === '1m' ? 'شهر' :
                            sale.warrantyPeriod === '3m' ? '3 أشهر' :
                            sale.warrantyPeriod === '6m' ? '6 أشهر' :
                            sale.warrantyPeriod === '1y' ? 'سنة' : 'بدون ضمان';

        events.push({
          id: `sale-${sale.id}-${deviceId}`,
          icon: <ShoppingCart className="h-5 w-5" />,
          title: 'بيع الجهاز',
          description: `تم بيع الجهاز للعميل "${sale.clientName}" ضمن فاتورة ${sale.soNumber}${sale.opNumber ? ` (الفاتورة الرسمية: ${sale.opNumber})` : ''}. ${deviceItem.salePrice ? `السعر: ${deviceItem.salePrice}` : ''} - الضمان: ${warrantyText}`,
          date: sale.date || sale.createdAt,
          color: 'bg-green-500/20 text-green-600',
          user: sale.employeeName || 'قسم المبيعات',
          type: 'sale',
          priority: 9,
          details: {
            soNumber: sale.soNumber,
            opNumber: sale.opNumber,
            clientName: sale.clientName,
            clientPhone: sale.clientPhone,
            clientAddress: sale.clientAddress,
            salePrice: deviceItem.salePrice,
            cost: deviceItem.cost,
            profit: deviceItem.salePrice && deviceItem.cost ? (deviceItem.salePrice - deviceItem.cost) : null,
            warrantyPeriod: sale.warrantyPeriod,
            warrantyText: warrantyText,
            paymentMethod: sale.paymentMethod,
            discount: sale.discount,
            warehouseName: sale.warehouseName,
            notes: sale.notes,
            attachments: sale.attachmentName
          }
        });
      }
    });

    // 9. المرتجعات والاستبدال - Returns and Replacements
    const relatedReturns = (allData.returns || []).filter((returnOrder: any) => {
      if (!returnOrder.items) {
        // للتوافق مع البيانات القديمة، تحقق من البيانات المباشرة
        return returnOrder.deviceId === deviceId || returnOrder.replacementDeviceId === deviceId;
      }
      const items = Array.isArray(returnOrder.items) ? returnOrder.items : JSON.parse(returnOrder.items || '[]');
      return items.some((item: any) => 
        item.deviceId === deviceId || item.replacementDeviceId === deviceId
      );
    });

    relatedReturns.forEach((returnOrder: any) => {
      // التعامل مع البيانات الجديدة والقديمة
      let items = [];
      if (returnOrder.items) {
        items = Array.isArray(returnOrder.items) ? returnOrder.items : JSON.parse(returnOrder.items || '[]');
      } else {
        // للتوافق مع البيانات القديمة
        items = [{
          deviceId: returnOrder.deviceId,
          replacementDeviceId: returnOrder.replacementDeviceId,
          returnReason: returnOrder.returnReason,
          returnType: returnOrder.returnType,
          model: returnOrder.model
        }];
      }

      const returnedItem = items.find((item: any) => item.deviceId === deviceId);
      const replacementItem = items.find((item: any) => item.replacementDeviceId === deviceId);

      if (returnedItem) {
        // الجهاز المرتجع
        const returnColor = returnedItem.returnReason?.includes('عطل') ? 'bg-red-500/20 text-red-600' :
                           returnedItem.returnReason?.includes('تالف') ? 'bg-orange-500/20 text-orange-600' :
                           returnedItem.returnReason?.includes('عيب') ? 'bg-yellow-500/20 text-yellow-600' :
                           'bg-red-500/20 text-red-600';

        // حساب المدة بين البيع والإرجاع
        const originalSale = relatedSales.find((sale: any) => 
          sale.soNumber === (returnOrder.soNumber || returnOrder.originalSaleInfo?.soNumber)
        );
        const daysBetween = originalSale ? 
          Math.floor((new Date(returnOrder.date).getTime() - new Date(originalSale.date).getTime()) / (1000 * 60 * 60 * 24)) : null;

        events.push({
          id: `return-${returnOrder.id}-${deviceId}`,
          icon: <Undo2 className="h-5 w-5" />,
          title: 'إرجاع الجهاز',
          description: `تم إرجاع الجهاز من العميل "${returnOrder.clientName}" في أمر المرتجع ${returnOrder.roNumber || returnOrder.returnNumber}. السبب: ${returnedItem.returnReason || 'غير محدد'}${daysBetween ? ` بعد ${daysBetween} يوم من البيع` : ''}${returnedItem.replacementDeviceId ? ` مع تقديم جهاز بديل` : ''}`,
          date: returnOrder.date || returnOrder.processedDate || returnOrder.createdAt,
          color: returnColor,
          user: returnOrder.employeeName || returnOrder.processedBy || 'قسم المرتجعات',
          type: 'return',
          priority: 10,
          details: {
            returnOrderNumber: returnOrder.roNumber || returnOrder.returnNumber,
            opReturnNumber: returnOrder.opReturnNumber,
            clientName: returnOrder.clientName,
            returnReason: returnedItem.returnReason,
            returnType: returnedItem.returnType,
            originalSaleNumber: returnOrder.soNumber,
            originalSaleDate: originalSale?.date,
            daysBetweenSaleAndReturn: daysBetween,
            hasReplacement: !!returnedItem.replacementDeviceId,
            replacementDeviceId: returnedItem.replacementDeviceId,
            refundAmount: returnedItem.refundAmount,
            condition: returnedItem.condition,
            notes: returnOrder.notes,
            status: returnOrder.status,
            attachments: returnOrder.attachments
          }
        });
      }

      if (replacementItem) {
        // الجهاز البديل
        events.push({
          id: `replacement-${returnOrder.id}-${deviceId}`,
          icon: <Replace className="h-5 w-5" />,
          title: 'جهاز بديل',
          description: `تم صرف هذا الجهاز كبديل للعميل "${returnOrder.clientName}" في أمر المرتجع ${returnOrder.roNumber || returnOrder.returnNumber} عن الجهاز الأصلي. ${replacementItem.additionalCost ? `تكلفة إضافية: ${replacementItem.additionalCost}` : 'بدون تكلفة إضافية'}`,
          date: returnOrder.date || returnOrder.processedDate || returnOrder.createdAt,
          color: 'bg-blue-500/20 text-blue-600',
          user: returnOrder.employeeName || returnOrder.processedBy || 'قسم المرتجعات',
          type: 'replacement',
          priority: 11,
          details: {
            returnOrderNumber: returnOrder.roNumber || returnOrder.returnNumber,
            clientName: returnOrder.clientName,
            originalDeviceId: replacementItem.originalDeviceId,
            replacementReason: replacementItem.returnReason,
            additionalCost: replacementItem.additionalCost,
            newWarrantyPeriod: replacementItem.newWarrantyPeriod,
            notes: returnOrder.notes,
            status: returnOrder.status
          }
        });
      }
    });

    // ترتيب الأحداث حسب التاريخ (من الأقدم إلى الأحدث)
    const sortedEvents = events.sort((a, b) => {
      const dateA = new Date(a.date).getTime();
      const dateB = new Date(b.date).getTime();
      
      // إذا كانت التواريخ متساوية، رتب حسب الأولوية
      if (dateA === dateB) {
        return a.priority - b.priority;
      }
      
      return dateA - dateB;
    });

    return sortedEvents;

  }, [deviceId, allData]);
}

export default useComprehensiveDeviceHistory;
