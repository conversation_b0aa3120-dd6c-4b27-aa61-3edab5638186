'use client';

import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { StoreProvider, useStore } from '@/context/store';
import { StocktakeStoreProvider } from '@/context/stocktake-store';
import {
  SidebarProvider,
  Sidebar,
  SidebarHeader,
  SidebarTrigger,
  SidebarContent,
  SidebarInset,
  SidebarFooter,
} from '@/components/ui/sidebar';
import { MainNav } from '@/components/main-nav';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Bell, Settings, LogOut, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useEffect, useMemo, useRef, useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import { AccountSettingsModal } from '@/components/account-settings-modal';
import { User } from '@/lib/types';

const pageTitles: { [key: string]: string } = {
  '/dashboard': 'لوحة التحكم',
  '/track': 'تتبع الجهاز',
  '/inventory': 'المخزون',
  '/sales': 'المبيعات',
  '/supply': 'أوامر التوريد',
  '/clients': 'العملاء والموردين',

  '/returns': 'المرتجعات',
  '/warehouses': 'إدارة المخازن',
  '/maintenance': 'الصيانة',
  '/maintenance-transfer': 'استلام وتسليم الصيانة',
  '/grading': 'الفحص والتقييم',
  '/warehouse-transfer': 'التحويل المخزني',
  '/users': 'إدارة المستخدمين',
  '/reports': 'التقارير',
  '/reports/model-reports': 'تقارير الموديلات',
  '/reports/client-reports': 'تقارير العملاء',
  '/reports/supplier-reports': 'تقارير الموردين',
  '/reports/maintenance-reports': 'تقارير الصيانة',
  '/reports/grading-reports': 'تقارير التقييم',
  '/reports/employee-reports': 'تقارير الموظفين',
  '/reports/operations-log': 'سجل العمليات',
  '/stocktaking': 'الجرد والمطابقة',
  '/settings': 'إعدادات النظام',
  '/requests': 'طلبات الموظفين',
  '/messaging': 'المراسلات الداخلية',
};

function Notifications() {
  const store = useStore();
  const { toast } = useToast();
  const router = useRouter();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Get store values with safe defaults
  const { internalMessages = [], currentUser, updateMessage, isLoading = true } = store || {};

  // Move ALL hooks BEFORE any conditional returns
  const unreadMessages = useMemo(() => {
    if (!currentUser) return [];
    return internalMessages
      .filter(
        (m) =>
          (m.recipientId === currentUser.id || m.recipientId === 0) &&
          !m.isRead,
      )
      .sort(
        (a, b) =>
          new Date(b.sentDate).getTime() - new Date(a.sentDate).getTime()
    );
  }, [internalMessages, currentUser]);

  const prevUnreadCount = useRef(unreadMessages.length);

  useEffect(() => {
    // Initialize audio only on client side
    if (typeof window !== 'undefined') {
      try {
        audioRef.current = new Audio('/sounds/notification.mp3');
        // Preload and handle errors gracefully
        audioRef.current.addEventListener('error', () => {
          console.warn('Notification sound file not found');
          audioRef.current = null;
        });
      } catch (error) {
        console.warn('Could not initialize notification audio:', error);
        audioRef.current = null;
      }
    }
  }, []);

  // Temporary permissions
  const permissions = { create: true, edit: true, delete: true };

  // Effect for new message notification
  useEffect(() => {
    if (unreadMessages.length > prevUnreadCount.current) {
      const newMessage = unreadMessages[0];
      toast({
        title: `رسالة جديدة من ${newMessage.senderName}`,
        description: newMessage.text.substring(0, 50) + '...',
        duration: 20000,
        className: 'cursor-pointer hover:bg-accent',
        onClick: () => {
          router.push('/messaging');
        },
      });
      audioRef.current
        ?.play()
        .catch((e) => console.error('Error playing audio:', e));
    }
    prevUnreadCount.current = unreadMessages.length;
  }, [unreadMessages, toast, router]);

  // Effect for reminder notification every 5 minutes
  useEffect(() => {
    const intervalId = setInterval(
      () => {
        const currentUnreadMessages = internalMessages.filter(
          (m) =>
            currentUser &&
            (m.recipientId === currentUser.id || m.recipientId === 0) &&
            !m.isRead
    );

        if (currentUnreadMessages.length > 0) {
          toast({
            title: `تذكير: لديك ${currentUnreadMessages.length} رسائل غير مقروءة`,
            description: 'يرجى مراجعة صندوق الوارد الخاص بك للرد عليها.',
            duration: 20000,
            className: 'cursor-pointer hover:bg-accent',
            onClick: () => {
              router.push('/messaging');
            },
          });
        }
      },
      5 * 60 * 1000
    ); // 5 minutes

    return () => clearInterval(intervalId);
  }, [internalMessages, currentUser, router, toast]);

  // NOW we can have conditional returns AFTER all hooks
  if (!store || isLoading) {
    return null;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadMessages.length > 0 && (
            <span className="absolute top-1 right-1 flex h-3 w-3">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">الإشعارات</h4>
            <p className="text-sm text-muted-foreground">
              لديك {unreadMessages.length} رسالة غير مقروءة.
            </p>
          </div>
          <div className="grid gap-2">
            {unreadMessages.slice(0, 5).map((message) => (
              <Link
                href="/messaging"
                key={message.id}
                className="group grid grid-cols-[25px_1fr] items-start gap-3 rounded-md px-2 py-2 transition-colors hover:bg-accent"
              >
                <span className="flex h-2 w-2 translate-y-1 rounded-full bg-primary" />
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {message.senderName}
                  </p>
                  <p className="text-sm text-muted-foreground truncate">
                    {message.text}
                  </p>
                  <p className="text-xs text-muted-foreground/80">
                    {formatDistanceToNow(new Date(message.sentDate), {
                      addSuffix: true,
                      locale: ar,
                    })}
                  </p>
                </div>
              </Link>
            ))}
          </div>
          {unreadMessages.length === 0 && (
            <p className="text-sm text-center text-muted-foreground py-4">
              لا توجد إشعارات جديدة.
            </p>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}

function LayoutComponent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const store = useStore();
  
  const [isEmployeesModalOpen, setIsEmployeesModalOpen] = useState(false);
  const [isAccountSettingsModalOpen, setIsAccountSettingsModalOpen] =
    useState(false);

  // Get store values with safe defaults
  const { currentUser, users = [], updateUser, isLoading = true } = store || {};

  // Move useMemo BEFORE any conditional returns to maintain hooks order
  const connectedEmployees = useMemo(() => {
    // This is a mock. In a real app, you'd have a way to track connected users.
    if (!users || !Array.isArray(users)) return [];
    return users.map((user) => ({
      ...user,
      isConnected: user.id % 2 === 0, // Mock: every other user is connected
      lastActivity: user.lastLogin ? new Date(user.lastLogin) : new Date(),
    }));
  }, [users]);

  // NOW we can have conditional returns AFTER all hooks
  if (!store || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>جاري تحميل التطبيق...</p>
        </div>
      </div>
    );
  }

  const title = pageTitles[pathname] || 'DeviceFlow';

  const handleLogout = () => {
    // Implement actual logout logic here
    console.log('User logged out');
    router.push('/login'); // Redirect to login page
  };

  const handleUpdateUser = (
    updatedUser: Partial<User>,
    newPassword?: string,
    oldPassword?: string,
  ) => {
    if (currentUser) {
      // In a real app, you'd send this to a backend API
      console.log('Updating user:', updatedUser);
      if (newPassword) {
        console.log('Updating password for', currentUser.username);
        // Simulate password change success/failure
        if (oldPassword === 'password123') {
          // Mock old password check
          toast({
            title: 'تم تغيير كلمة المرور',
            description: 'تم تغيير كلمة المرور بنجاح.',
            duration: 3000,
          });
        } else {
          toast({
            title: 'خطأ',
            description: 'كلمة المرور الحالية غير صحيحة.',
            variant: 'destructive',
            duration: 3000,
          });
          return;
        }
      }
      // Update local state for demonstration
      updateUser({ ...currentUser, ...updatedUser });
      toast({
        title: 'تم تحديث الملف الشخصي',
        description: 'تم تحديث معلومات حسابك بنجاح.',
        duration: 3000,
      });
    }
  };

  const handleAccountSettings = () => {
    setIsAccountSettingsModalOpen(true);
  };

  const handleViewConnectedEmployees = () => {
    setIsEmployeesModalOpen(true);
  };

  return (
    <SidebarProvider>
      <Sidebar side="right" collapsible="icon">
        <SidebarHeader>
          <div className="flex items-center gap-2">
            <h1 className="text-xl font-semibold">DeviceFlow</h1>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <MainNav />
        </SidebarContent>
        <SidebarFooter>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-start h-auto p-2"
              >
                <Avatar className="h-9 w-9">
                  <AvatarImage
                    src={currentUser?.photo || 'https://placehold.co/40x40'}
                    alt={currentUser?.name || 'User'}
                  />
                  <AvatarFallback>
                    {currentUser?.name?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col ml-3 items-start">
                  <span className="text-sm font-medium">
                    {currentUser?.name || 'المستخدم'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {currentUser?.email || ''}
                  </span>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel>حسابي</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleAccountSettings}>
                <Settings className="ml-2 h-4 w-4" />
                إعدادات الحساب
              </DropdownMenuItem>
              {currentUser?.permissions?.users?.view && (
                <DropdownMenuItem onClick={handleViewConnectedEmployees}>
                  <Users className="ml-2 h-4 w-4" />
                  عرض الموظفين المتصلين
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="ml-2 h-4 w-4" />
                تسجيل الخروج
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarFooter>
      </Sidebar>
      <SidebarInset>
        <header className="flex h-14 items-center justify-between gap-4 border-b bg-background/95 px-4 backdrop-blur-sm sm:px-6">
          <div className="flex items-center gap-2">
            <SidebarTrigger />
            <h1 className="text-lg font-semibold md:text-xl">{title}</h1>
          </div>
          <div className="flex items-center gap-2">
            <Notifications />
          </div>
        </header>
        <main className="flex-1 overflow-auto p-4 sm:p-6">{children}</main>
      </SidebarInset>

      {/* Connected Employees Modal */}
      <Dialog
        open={isEmployeesModalOpen}
        onOpenChange={setIsEmployeesModalOpen}
      >
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>الموظفون المتصلون</DialogTitle>
            <DialogDescription>عرض حالة اتصال الموظفين.</DialogDescription>
          </DialogHeader>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم</TableHead>
                <TableHead>اسم المستخدم</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>آخر نشاط</TableHead>
                <TableHead>إجراء</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {connectedEmployees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell>{employee.name}</TableCell>
                  <TableCell>{employee.username}</TableCell>
                  <TableCell>
                    <Badge
                      variant={employee.isConnected ? 'default' : 'secondary'}
                    >
                      {employee.isConnected ? 'متصل' : 'غير متصل'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {employee.lastActivity.toLocaleString('ar-EG')}
                  </TableCell>
                  <TableCell>
                    {!employee.isConnected && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() =>
                          console.log(`Logging out ${employee.name}`)
                        }
                      >
                        تسجيل خروج
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <DialogFooter>
            <Button onClick={() => setIsEmployeesModalOpen(false)}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Account Settings Modal */}
      <AccountSettingsModal
        isOpen={isAccountSettingsModalOpen}
        onClose={() => setIsAccountSettingsModalOpen(false)}
        currentUser={currentUser}
        onUpdateUser={handleUpdateUser}
      />
    </SidebarProvider>
  );
}

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <StoreProvider>
      <StocktakeStoreProvider>
        <LayoutComponent>{children}</LayoutComponent>
      </StocktakeStoreProvider>
    </StoreProvider>
  );
}
