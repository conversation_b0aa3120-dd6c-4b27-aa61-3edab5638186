
// Required API endpoints:
// - /api/devices
// - /api/sales
// - /api/returns
// - /api/supply
// - /api/suppliers
// - /api/evaluations
// - /api/maintenance-orders
// - /api/warehouse-transfers
'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { apiClient, handleApiResponse } from '@/lib/api-client';
import { formatDateTime } from '@/lib/date-utils';
import { ar } from 'date-fns/locale';
import './track.css';
import './enhanced-styles.css';
import './enhanced-track-styles.css';
import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Barcode,
  Package,
  Wrench,
  ShoppingCart,
  Undo2,
  ClipboardCheck,
  Shuffle,
  User,
  PackageCheck,
  ShieldCheck,
  FileText,
  Replace,
  Printer,
  FileDown,
  AlertCircle,
} from 'lucide-react';
import {
  addDays,
  addMonths,
  addYears,
  isAfter,
  formatDistanceToNowStrict,
  format,
} from 'date-fns';
import { exportDataToPDF, exportHTMLToPDF, exportDeviceTrackingReport, exportDeviceTrackingReportDirect, printElement, printDeviceData } from '@/lib/export-utils/html-to-pdf';
import { createArabicPDFWithCanvas } from '@/lib/export-utils/canvas-pdf-enhanced';
import { exportDeviceTrackingReportHTML } from '@/lib/export-utils/enhanced-html-export';
import { printDeviceTrackingReport, printElementWithSettings } from '@/lib/device-tracking-utils';
import ReportPreview from '@/components/ReportPreview';
import DeviceTrackingFilters from './DeviceTrackingFilters';
import DeviceDetailsSection from './DeviceDetailsSection';
import DeviceHistoryTimeline from './DeviceHistoryTimeline';
import DeviceOperationDetails from './DeviceOperationDetails';
import DeviceAdvancedStats from './DeviceAdvancedStats';
import './print-styles.css';
import './enhanced-device-tracking.css';

// تم حذف الدالة المحلية واستبدالها بـ formatDateTime من date-utils.ts

type TimelineEvent = {
  icon: React.ReactNode;
  title: string;
  description: string;
  date: string; // ISO string for sorting
  color: string;
  user?: string;
  formattedDate?: string;
  details?: any; // إضافة خاصية التفاصيل
  id?: string; // إضافة خاصية الهوية الاختيارية
};

export default function TrackPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [devices, setDevices] = useState([]);
  const [sales, setSales] = useState([]);
  const [returns, setReturns] = useState([]);
  const [supplyOrders, setSupplyOrders] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [evaluationOrders, setEvaluationOrders] = useState([]);
  const [maintenanceHistory, setMaintenanceHistory] = useState([]);
  const [maintenanceOrders, setMaintenanceOrders] = useState([]);
  const [maintenanceReceiptOrders, setMaintenanceReceiptOrders] = useState([]);
  const [warehouseTransfers, setWarehouseTransfers] = useState([]);
  const [maintenanceLogs, setMaintenanceLogs] = useState([]);
  const [maintenanceReceipts, setMaintenanceReceipts] = useState([]);
  const [deliveryOrders, setDeliveryOrders] = useState([]);

  // API functions
  const fetchAllData = async () => {
    setIsLoading(true);
    try {
      const [
        devicesRes,
        salesRes,
        returnsRes,
        supplyRes,
        suppliersRes,
        evaluationsRes,
        maintenanceRes,
        maintenanceOrdersRes,
        maintenanceReceiptsRes,
        warehouseTransfersRes,
        deliveryOrdersRes
      ] = await Promise.all([
        apiClient.get('/api/devices?view=simple'),
        apiClient.get('/api/sales?view=simple'),
        apiClient.get('/api/returns?view=simple'),
        apiClient.get('/api/supply?view=simple'),
        apiClient.get('/api/suppliers?view=simple'),
        apiClient.get('/api/evaluations?view=simple'),
        apiClient.get('/api/maintenance-logs?view=simple'),
        apiClient.get('/api/maintenance-orders?view=simple'),
        apiClient.get('/api/maintenance-receipts?view=simple'),
        apiClient.get('/api/warehouse-transfers'),
        apiClient.get('/api/delivery-orders?view=simple')
      ]);

      if (devicesRes.ok) setDevices(await handleApiResponse(devicesRes));
      if (salesRes.ok) setSales(await handleApiResponse(salesRes));
      if (returnsRes.ok) setReturns(await handleApiResponse(returnsRes));
      if (supplyRes.ok) setSupplyOrders(await handleApiResponse(supplyRes));
      if (suppliersRes.ok) setSuppliers(await handleApiResponse(suppliersRes));
      if (evaluationsRes.ok) setEvaluationOrders(await handleApiResponse(evaluationsRes));
      if (maintenanceRes.ok) setMaintenanceLogs(await handleApiResponse(maintenanceRes));
      if (maintenanceOrdersRes.ok) setMaintenanceOrders(await handleApiResponse(maintenanceOrdersRes));
      if (maintenanceReceiptsRes.ok) setMaintenanceReceipts(await handleApiResponse(maintenanceReceiptsRes));
      
      // تحميل أوامر التسليم - قراءة واحدة فقط
      if (deliveryOrdersRes.ok) {
        const deliveryOrders = await handleApiResponse(deliveryOrdersRes);
        setDeliveryOrders(deliveryOrders);
      }
      
      // تحميل التحويلات المخزنية
      if (warehouseTransfersRes.ok) {
        setWarehouseTransfers(await handleApiResponse(warehouseTransfersRes));
      } else {
        console.warn('Warehouse transfers API not available, using empty array');
        setWarehouseTransfers([]);
      }

    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل البيانات'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  const [imei, setImei] = useState('');
  const [searchedImei, setSearchedImei] = useState('');
  const [isCustomerView, setIsCustomerView] = useState(false);
  const [showReportPreview, setShowReportPreview] = useState(false);
  const [useCanvasMethod, setUseCanvasMethod] = useState(false);
  const [filteredTimelineEvents, setFilteredTimelineEvents] = useState<TimelineEvent[]>([]);

  useEffect(() => {
    const idFromQuery = searchParams.get('id');
    if (idFromQuery) {
      setImei(idFromQuery);
      setSearchedImei(idFromQuery);
    }
  }, [searchParams]);

  const handleSearch = () => {
    if (!imei) return;
    setSearchedImei(imei);
    const params = new URLSearchParams(searchParams);
    params.set('id', imei);
    router.replace(`/track?${params.toString()}`);
  };

  const fullTimelineEvents = useMemo((): TimelineEvent[] => {
    if (!searchedImei) return [];

    const events: TimelineEvent[] = [];
    const device = (devices as any[]).find((d: any) => d.id === searchedImei);

    console.log('🔍 Building comprehensive timeline for device:', searchedImei);
    console.log('Available data sources:', {
      devices: devices?.length || 0,
      sales: sales?.length || 0,
      returns: returns?.length || 0,
      supplyOrders: supplyOrders?.length || 0,
      evaluationOrders: evaluationOrders?.length || 0,
      maintenanceOrders: maintenanceOrders?.length || 0,
      maintenanceReceiptOrders: maintenanceReceiptOrders?.length || 0,
      maintenanceLogs: maintenanceLogs?.length || 0,
      warehouseTransfers: warehouseTransfers?.length || 0,
      deliveryOrders: deliveryOrders?.length || 0
    });

    // 1. Supply Event - توريد الجهاز (محسن)
    const relatedSupplyOrders = (supplyOrders as any[]).filter((order: any) => {
      if (!order.items) return false;
      try {
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
        return items.some((item: any) => 
          item.imei === searchedImei || 
          item.deviceId === searchedImei || 
          item.id === searchedImei
        );
      } catch (error) {
        console.warn('Error parsing supply order items:', error);
        return false;
      }
    });

    console.log('🏭 Found supply orders:', relatedSupplyOrders.length);
    relatedSupplyOrders.forEach((order: any) => {
      const supplier = (suppliers as any[]).find((s: any) => s.id === order.supplierId);
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => 
        item.imei === searchedImei || item.deviceId === searchedImei || item.id === searchedImei
      );

      events.push({
        icon: <Package className="h-5 w-5" />,
        title: 'توريد الجهاز',
        description: `تم استلام الجهاز من المورد '${supplier?.name || 'غير معروف'}' ضمن أمر التوريد ${order.supplyOrderId || order.orderNumber}. ${deviceItem?.purchasePrice ? `سعر الشراء: ${deviceItem.purchasePrice}` : ''} ${order.warehouseName ? `في مخزن: ${order.warehouseName}` : ''}`,
        date: order.supplyDate || order.date || order.createdAt,
        color: 'bg-cyan-500/20 text-cyan-400',
        user: order.employeeName,
        details: {
          supplyOrderId: order.supplyOrderId || order.orderNumber,
          supplierName: supplier?.name,
          supplierCode: supplier?.code,
          purchasePrice: deviceItem?.purchasePrice,
          warehouseName: order.warehouseName,
          invoiceNumber: order.invoiceNumber,
          model: deviceItem?.model,
          condition: deviceItem?.condition,
          notes: order.notes,
          attachments: order.attachmentName,
          receivedBy: order.employeeName,
          receivedDate: order.supplyDate || order.date
        }
      });
    });

    // 2. Evaluation Events - فحص وتقييم (محسن)
    const relatedEvaluations = (evaluationOrders as any[]).filter((order: any) => {
      if (!order.items) return false;
      try {
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
        return items.some((item: any) => item.deviceId === searchedImei);
      } catch (error) {
        console.warn('Error parsing evaluation items:', error);
        return false;
      }
    });

    console.log('🔍 Found evaluation orders:', relatedEvaluations.length);
    relatedEvaluations.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === searchedImei);

      if (deviceItem) {
        // تحديد لون التقييم بناءً على الدرجة النهائية
        const gradeColor = deviceItem.finalGrade === 'A' ? 'bg-green-500/20 text-green-400' :
                          deviceItem.finalGrade === 'B' ? 'bg-blue-500/20 text-blue-400' :
                          deviceItem.finalGrade === 'C' ? 'bg-yellow-500/20 text-yellow-400' :
                          deviceItem.finalGrade === 'D' ? 'bg-orange-500/20 text-orange-400' :
                          'bg-red-500/20 text-red-400';

        // إنشاء وصف مفصل للتقييم
        const gradeDetails = [
          `خارجي: ${deviceItem.externalGrade || 'غير محدد'}`,
          `شاشة: ${deviceItem.screenGrade || 'غير محدد'}`,
          `شبكة: ${deviceItem.networkGrade || 'غير محدد'}`
        ].join(' | ');

        const faultInfo = deviceItem.fault || deviceItem.damageType;
        const conditionText = deviceItem.condition || 'حالة غير محددة';
        const description = `تم فحص وتقييم الجهاز بواسطة ${order.employeeName}. التقييم النهائي: ${deviceItem.finalGrade || 'غير محدد'}. التفاصيل: ${gradeDetails}${faultInfo ? `. مشاكل مكتشفة: ${faultInfo}` : ''}. الحالة العامة: ${conditionText}. ${deviceItem.expectedPrice ? `السعر المتوقع: ${deviceItem.expectedPrice}` : ''}`;

        events.push({
          icon: <ClipboardCheck className="h-5 w-5" />,
          title: 'فحص وتقييم شامل',
          description: description,
          date: order.date,
          color: gradeColor,
          user: order.employeeName,
          details: {
            evaluationOrderId: order.orderId,
            finalGrade: deviceItem.finalGrade,
            externalGrade: deviceItem.externalGrade,
            screenGrade: deviceItem.screenGrade,
            networkGrade: deviceItem.networkGrade,
            batteryHealth: deviceItem.batteryHealth,
            cameraQuality: deviceItem.cameraQuality,
            soundQuality: deviceItem.soundQuality,
            touchSensitivity: deviceItem.touchSensitivity,
            fault: deviceItem.fault,
            damageType: deviceItem.damageType,
            condition: deviceItem.condition,
            expectedPrice: deviceItem.expectedPrice,
            marketValue: deviceItem.marketValue,
            evaluatedBy: order.employeeName,
            evaluationDate: order.date,
            deviceModel: deviceItem.model,
            testResults: deviceItem.testResults,
            recommendations: deviceItem.recommendations,
            needsMaintenance: deviceItem.needsMaintenance,
            notes: order.notes,
            attachments: order.attachmentName,
            warehouseName: order.warehouseName,
            acknowledgedBy: order.acknowledgedBy,
            status: order.status
          }
        });

        // إضافة حدث إضافي إذا كان الجهاز يحتاج صيانة
        if (deviceItem.needsMaintenance) {
          events.push({
            icon: <Wrench className="h-5 w-5" />,
            title: 'توصية بإرسال للصيانة',
            description: `بناءً على نتائج التقييم، تم تحديد أن الجهاز يحتاج إلى صيانة قبل الاستخدام أو البيع. ${deviceItem.recommendations ? `التوصيات: ${deviceItem.recommendations}` : ''}`,
            date: order.date,
            color: 'bg-orange-500/20 text-orange-400',
            user: order.employeeName,
            details: {
              reason: 'توصية من التقييم',
              originalEvaluationId: order.orderId,
              maintenanceRecommendation: deviceItem.recommendations,
              urgency: deviceItem.maintenanceUrgency || 'عادي',
              estimatedCost: deviceItem.estimatedMaintenanceCost
            }
          });
        }
      }
    });

    // 3. Maintenance Send Events - إرسال الأجهزة للصيانة (محسن وشامل)
    const relatedMaintenanceOrders = (maintenanceOrders as any[]).filter((order: any) => {
      if (!order.items) return false;
      try {
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
        return items.some((item: any) => item.deviceId === searchedImei || item.id === searchedImei);
      } catch (error) {
        console.warn('Error parsing maintenance order items:', error);
        return false;
      }
    });

    console.log('🔧 Found maintenance orders:', relatedMaintenanceOrders.length);
    relatedMaintenanceOrders.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === searchedImei || item.id === searchedImei);

      if (deviceItem) {
        // تحديد سبب الإرسال للصيانة
        const maintenanceReason = deviceItem.fault || deviceItem.damageType || deviceItem.issueDescription || 'صيانة عامة';
        const source = order.source === 'direct' ? 'استلام مباشر في الصيانة' : 'إرسال للصيانة';
        const priorityText = deviceItem.priority === 'urgent' ? ' (عاجل)' : 
                            deviceItem.priority === 'high' ? ' (أولوية عالية)' : '';
        
        events.push({
          icon: <Wrench className="h-5 w-5" />,
          title: `${source}${priorityText}`,
          description: `تم ${source.toLowerCase()} ضمن أمر ${order.orderNumber}. السبب: ${maintenanceReason}. ${deviceItem.expectedCost ? `التكلفة المتوقعة: ${deviceItem.expectedCost}` : ''} ${order.maintenanceEmployeeName ? `المسؤول: ${order.maintenanceEmployeeName}` : ''}`,
          date: order.date || order.createdAt,
          color: deviceItem.priority === 'urgent' ? 'bg-red-500/20 text-red-400' : 'bg-orange-500/20 text-orange-400',
          user: order.employeeName,
          details: {
            orderNumber: order.orderNumber,
            referenceNumber: order.referenceNumber,
            fault: deviceItem.fault,
            damageType: deviceItem.damageType,
            issueDescription: deviceItem.issueDescription,
            deviceCondition: deviceItem.condition,
            notes: deviceItem.notes || order.notes,
            maintenanceEmployee: order.maintenanceEmployeeName,
            maintenanceEmployeeId: order.maintenanceEmployeeId,
            source: order.source,
            status: order.status,
            priority: deviceItem.priority,
            expectedCost: deviceItem.expectedCost,
            estimatedDuration: deviceItem.estimatedDuration,
            attachments: order.attachmentName,
            createdBy: order.employeeName,
            createdDate: order.date
          }
        });

        // إضافة أحداث إضافية بناءً على حالة الجهاز
        if (device?.status === 'بانتظار قطع غيار') {
          events.push({
            icon: <AlertCircle className="h-5 w-5" />,
            title: 'في انتظار قطع غيار',
            description: `الجهاز حالياً في انتظار توفر قطع الغيار اللازمة للإصلاح ضمن أمر الصيانة ${order.orderNumber}. ${deviceItem.requiredParts ? `القطع المطلوبة: ${deviceItem.requiredParts}` : ''}`,
            date: new Date().toISOString(),
            color: 'bg-yellow-500/20 text-yellow-400',
            user: 'قسم الصيانة',
            details: {
              currentStatus: device.status,
              orderNumber: order.orderNumber,
              waitingReason: 'قطع غيار غير متوفرة',
              requiredParts: deviceItem.requiredParts,
              estimatedDelivery: deviceItem.partsDeliveryDate
            }
          });
        }

        if (device?.status === 'مراجعة الطلب من الإدارة') {
          events.push({
            icon: <FileText className="h-5 w-5" />,
            title: 'مراجعة إدارية',
            description: `الجهاز تحت المراجعة الإدارية لاتخاذ قرار بشأن استكمال عملية الصيانة ضمن أمر ${order.orderNumber}. ${deviceItem.adminNotes ? `ملاحظات الإدارة: ${deviceItem.adminNotes}` : ''}`,
            date: new Date().toISOString(),
            color: 'bg-purple-500/20 text-purple-400',
            user: 'الإدارة',
            details: {
              currentStatus: device.status,
              orderNumber: order.orderNumber,
              reviewReason: 'يتطلب موافقة إدارية',
              expectedCost: deviceItem.expectedCost,
              adminNotes: deviceItem.adminNotes
            }
          });
        }
      }
    });

    // 4. Maintenance Receipt Events - استلام الأجهزة من الصيانة (شامل ومحسن)
    const relatedMaintenanceReceipts = [
      ...(maintenanceReceiptOrders || []),
      ...(maintenanceReceipts || [])
    ].filter((order: any) => {
      if (!order.items) return false;
      try {
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
        return items.some((item: any) => item.deviceId === searchedImei);
      } catch (error) {
        console.warn('Error parsing maintenance receipt items:', error);
        return false;
      }
    });

    console.log('📦 Found maintenance receipts:', relatedMaintenanceReceipts.length);
    relatedMaintenanceReceipts.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === searchedImei);

      if (deviceItem) {
        const resultText = deviceItem.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                          deviceItem.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل فنياً' :
                          deviceItem.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف فيزيائياً' :
                          deviceItem.result === 'PartiallyRepaired' ? 'تم الإصلاح جزئياً' :
                          deviceItem.result || 'نتيجة غير محددة';

        const receiptColor = deviceItem.result === 'Repaired' ? 'bg-green-500/20 text-green-400' : 
                            deviceItem.result === 'PartiallyRepaired' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-red-500/20 text-red-400';

        const workDone = deviceItem.workPerformed || deviceItem.repairDescription || 'لا توجد تفاصيل';
        
        events.push({
          icon: <PackageCheck className="h-5 w-5" />,
          title: 'استلام من الصيانة (نتيجة نهائية)',
          description: `تم استلام الجهاز من قسم الصيانة ضمن إيصال ${order.receiptNumber}. النتيجة النهائية: ${resultText}. ${deviceItem.repairCost ? `التكلفة الفعلية: ${deviceItem.repairCost}` : ''} ${deviceItem.repairDuration ? `مدة الإصلاح: ${deviceItem.repairDuration} يوم` : ''} العمل المنجز: ${workDone}`,
          date: order.date || order.createdAt,
          color: receiptColor,
          user: order.employeeName,
          details: {
            receiptNumber: order.receiptNumber,
            referenceNumber: order.referenceNumber,
            result: deviceItem.result,
            resultText: resultText,
            originalFault: deviceItem.fault,
            damage: deviceItem.damage,
            damageType: deviceItem.damageType,
            repairDescription: deviceItem.repairDescription,
            workPerformed: deviceItem.workPerformed,
            repairCost: deviceItem.repairCost,
            estimatedCost: deviceItem.estimatedCost,
            repairDuration: deviceItem.repairDuration,
            partsUsed: deviceItem.partsUsed,
            partsReplaced: deviceItem.partsReplaced,
            testResults: deviceItem.testResults,
            qualityCheck: deviceItem.qualityCheck,
            warrantyPeriod: deviceItem.warrantyPeriod,
            maintenanceEmployee: order.maintenanceEmployeeName,
            receivingEmployee: order.employeeName,
            warehouseName: order.warehouseName,
            notes: deviceItem.notes || order.notes,
            attachments: order.attachmentName,
            completedDate: order.date,
            status: order.status
          }
        });

        // إضافة حدث للتسليم للمخزن إذا كان في انتظار الاستلام
        if (device?.status === 'بانتظار استلام في المخزن') {
          events.push({
            icon: <Shuffle className="h-5 w-5" />,
            title: 'بانتظار الاستلام في المخزن',
            description: `الجهاز تم تسليمه من الصيانة وهو الآن بانتظار الاستلام الرسمي في مخزن "${order.warehouseName}". النتيجة النهائية: ${resultText}`,
            date: new Date().toISOString(),
            color: 'bg-blue-500/20 text-blue-400',
            user: 'قسم الصيانة',
            details: {
              currentStatus: device.status,
              receiptNumber: order.receiptNumber,
              finalResult: deviceItem.result,
              awaitingWarehouse: order.warehouseName,
              completedWork: deviceItem.workPerformed,
              finalCost: deviceItem.repairCost
            }
          });
        }
      }
    });

    // 5. Maintenance Logs Events - سجلات الصيانة المفصلة
    const relatedMaintenanceLogs = (maintenanceLogs || []).filter((log: any) => 
      log.deviceId === searchedImei
    );

    console.log('📋 Found maintenance logs:', relatedMaintenanceLogs.length);
    relatedMaintenanceLogs.forEach((log: any) => {
      const resultText = log.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                        log.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل' :
                        log.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف' :
                        log.result === 'PartiallyRepaired' ? 'تم الإصلاح جزئياً' :
                        log.result || 'في انتظار النتيجة';

      const statusText = log.status === 'pending' ? 'قيد العمل' :
                        log.status === 'acknowledged' ? 'مكتمل ومستلم' :
                        log.status === 'completed' ? 'مكتمل' :
                        log.status || 'غير محدد';

      events.push({
        icon: <Wrench className="h-5 w-5" />,
        title: `سجل صيانة مفصل - ${statusText}`,
        description: `${log.notes || 'سجل صيانة شامل للجهاز'}. النتيجة النهائية: ${resultText}. ${log.model ? `الموديل: ${log.model}` : ''} ${log.warehouseName ? `المخزن: ${log.warehouseName}` : ''}`,
        date: log.repairDate || log.createdAt,
        color: log.result === 'Repaired' ? 'bg-green-500/20 text-green-400' : 
              log.result === 'PartiallyRepaired' ? 'bg-yellow-500/20 text-yellow-400' :
              log.status === 'pending' ? 'bg-orange-500/20 text-orange-400' :
              'bg-gray-500/20 text-gray-400',
        user: log.acknowledgedBy || log.maintenanceEmployee || 'فني الصيانة',
        details: {
          model: log.model,
          result: log.result,
          resultText: resultText,
          status: log.status,
          statusText: statusText,
          repairDetails: log.repairDetails,
          notes: log. notes,
          warehouseName: log.warehouseName,
          acknowledgedBy: log.acknowledgedBy,
          acknowledgedDate: log.acknowledgedDate,
          repairDate: log.repairDate,
          maintenanceEmployee: log.maintenanceEmployee,
          cost: log.cost,
          duration: log.duration
        }
      });

      // إضافة حدث منفصل للاستلام في المخزن
      if (log.status === 'acknowledged' && log.acknowledgedDate) {
        events.push({
          icon: <PackageCheck className="h-5 w-5" />,
          title: 'استلام رسمي في المخزن',
          description: `تم الاستلام الرسمي للجهاز في مخزن "${log.warehouseName}" بعد انتهاء أعمال الصيانة. المستلم: ${log.acknowledgedBy}`,
          date: log.acknowledgedDate,
          color: 'bg-purple-500/20 text-purple-400',
          user: log.acknowledgedBy || 'موظف المخزن',
          details: {
            acknowledgedBy: log.acknowledgedBy,
            acknowledgedDate: log.acknowledgedDate,
            warehouseName: log.warehouseName,
            originalResult: log.result,
            repairDate: log.repairDate,
            finalStatus: 'متاح في المخزن'
          }
        });
      }
    });
      const orderItems = Array.isArray(order.items) ? order.items :
                        (typeof order.items === 'string' ? JSON.parse(order.items) : []);

      const deviceItem = orderItems.find((item: any) => item.deviceId === searchedImei);
      if (deviceItem) {
        const resultText = deviceItem.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                          deviceItem.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل فنياً' :
                          deviceItem.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف فيزيائياً' :
                          deviceItem.result || 'غير محدد';

        events.push({
          icon: <PackageCheck className="h-5 w-5" />,
          title: 'استلام من الصيانة',
          description: `تم استلام الجهاز من قسم الصيانة ضمن أمر ${order.receiptNumber}. النتيجة النهائية: ${resultText}${deviceItem.notes ? `. تفاصيل إضافية: ${deviceItem.notes}` : ''}${deviceItem.repairCost ? `. تكلفة الإصلاح: ${deviceItem.repairCost}` : ''}`,
          date: order.date,
          color: deviceItem.result === 'Repaired' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400',
          user: order.employeeName,
          details: {
            receiptNumber: order.receiptNumber,
            referenceNumber: order.referenceNumber,
            result: deviceItem.result,
            resultText: resultText,
            fault: deviceItem.fault,
            damage: deviceItem.damage,
            damageType: deviceItem.damageType,
            notes: deviceItem.notes,
            repairDescription: deviceItem.repairDescription,
            repairCost: deviceItem.repairCost,
            maintenanceEmployee: order.maintenanceEmployeeName,
            receivingEmployee: order.employeeName,
            warehouseName: order.warehouseName,
            duration: deviceItem.repairDuration,
            partsUsed: deviceItem.partsUsed,
            attachments: order.attachmentName
          }
        });

        // إضافة حدث للتسليم للمخزن إذا كان الجهاز في انتظار الاستلام
        if (device?.status === 'بانتظار استلام في المخزن') {
          events.push({
            icon: <Shuffle className="h-5 w-5" />,
            title: 'بانتظار الاستلام في المخزن',
            description: `الجهاز تم تسليمه من الصيانة وهو الآن بانتظار الاستلام في المخزن المحدد.`,
            date: new Date().toISOString(),
            color: 'bg-blue-500/20 text-blue-400',
            user: 'قسم الصيانة',
            details: {
              currentStatus: device.status,
              receiptNumber: order.receiptNumber,
              finalResult: deviceItem.result,
              awaitingWarehouse: order.warehouseName
            }
          });
        }
      }
    });

    // 5. Maintenance History Events - سجل الصيانة القديم (للتوافق مع النظام القديم)
    (maintenanceHistory as any[]).forEach((log: any) => {
      if (log.deviceId === searchedImei) {
        // إضافة حدث إتمام الصيانة مع تفاصيل شاملة
        const resultText = log.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                          log.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل' :
                          log.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف' :
                          log.result || 'غير محدد';

        events.push({
          icon: <Wrench className="h-5 w-5" />,
          title: 'إتمام الصيانة (سجل قديم)',
          description: `تمت معالجة الجهاز في قسم الصيانة. النتيجة: ${resultText}${log.notes ? `. ملاحظات: ${log.notes}` : ''}`,
          date: log.repairDate,
          color: log.result === 'Repaired' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400',
          user: 'قسم الصيانة',
          details: {
            result: log.result,
            resultText: resultText,
            notes: log.notes,
            status: log.status,
            deviceModel: log.model,
            repairDate: log.repairDate
          }
        });

        // إضافة حدث الاستلام في المخزن إذا تم
        if (log.status === 'acknowledged' && log.acknowledgedDate) {
          events.push({
            icon: <PackageCheck className="h-5 w-5" />,
            title: 'استلام في المخزن (سجل قديم)',
            description: `تم استلام الجهاز في مخزن '${log.warehouseName || 'غير معروف'}' بعد انتهاء عملية الصيانة.`,
            date: log.acknowledgedDate,
            color: 'bg-purple-500/20 text-purple-400',
            user: log.acknowledgedBy || 'موظف المخزن',
            details: {
              warehouseName: log.warehouseName,
              acknowledgedBy: log.acknowledgedBy,
              acknowledgedDate: log.acknowledgedDate,
              originalRepairDate: log.repairDate,
              finalResult: log.result
            }
          });
        }
      }
    });

    // 5.5. Maintenance Logs Events - سجلات الصيانة البسيطة (جديد)
    (maintenanceLogs as any[]).forEach((log: any) => {
      if (log.deviceId === searchedImei) {
        const resultText = log.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                          log.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل' :
                          log.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف' :
                          log.result || 'في انتظار النتيجة';

        const statusText = log.status === 'pending' ? 'قيد العمل' :
                          log.status === 'acknowledged' ? 'مكتمل ومستلم' :
                          log.status || 'غير محدد';

        events.push({
          icon: <Wrench className="h-5 w-5" />,
          title: `سجل صيانة - ${statusText}`,
          description: `${log.notes ? log.notes : 'سجل صيانة للجهاز'}. النتيجة: ${resultText}`,
          date: log.repairDate,
          color: log.result === 'Repaired' ? 'bg-green-500/20 text-green-400' : 
                log.status === 'pending' ? 'bg-orange-500/20 text-orange-400' :
                'bg-gray-500/20 text-gray-400',
          user: log.acknowledgedBy || 'فني الصيانة',
          details: {
            model: log.model,
            repairDate: log.repairDate,
            result: log.result,
            resultText: resultText,
            status: log.status,
            statusText: statusText,
            notes: log.notes,
            warehouseName: log.warehouseName,
            acknowledgedBy: log.acknowledgedBy,
            acknowledgedDate: log.acknowledgedDate
          }
        });

        // إضافة حدث منفصل للاستلام إذا تم
        if (log.status === 'acknowledged' && log.acknowledgedDate) {
          events.push({
            icon: <PackageCheck className="h-5 w-5" />,
            title: 'استلام من الصيانة',
            description: `تم استلام الجهاز في المخزن بعد انتهاء الصيانة. ${log.warehouseName ? `المخزن: ${log.warehouseName}` : ''}`,
            date: log.acknowledgedDate,
            color: 'bg-purple-500/20 text-purple-400',
            user: log.acknowledgedBy || 'موظف المخزن',
            details: {
              acknowledgedBy: log.acknowledgedBy,
              acknowledgedDate: log.acknowledgedDate,
              warehouseName: log.warehouseName,
              originalResult: log.result,
              repairDate: log.repairDate
            }
          });
        }
      }
    });

    // 6. Warehouse Transfer Events - التحويلات المخزنية والتخويل المخزني (محسن)
    (warehouseTransfers as any[]).forEach((transfer: any) => {
      if (Array.isArray(transfer.items) && transfer.items.some((item: any) => item.deviceId === searchedImei)) {
        const transferType = transfer.transferType || 'نقل عادي';
        const statusText = transfer.status === 'completed' ? 'مكتمل' : 
                          transfer.status === 'pending' ? 'بانتظار الاستلام' :
                          transfer.status === 'cancelled' ? 'ملغي' : 'معلق';
        
        const transferItem = transfer.items.find((item: any) => item.deviceId === searchedImei);
        
        events.push({
          icon: <Shuffle className="h-5 w-5" />,
          title: `${transferType} (${statusText})`,
          description: `تم ${transferType.toLowerCase()} للجهاز من مخزن '${transfer.fromWarehouseName}' إلى مخزن '${transfer.toWarehouseName}'. أمر التحويل: ${transfer.transferNumber}. ${transferItem?.reason ? `السبب: ${transferItem.reason}` : ''}`,
          date: transfer.date,
          color: transfer.status === 'completed' ? 'bg-green-500/20 text-green-400' : 
                transfer.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-gray-500/20 text-gray-400',
          user: transfer.employeeName,
          details: {
            transferNumber: transfer.transferNumber,
            transferType: transferType,
            fromWarehouse: transfer.fromWarehouseName,
            toWarehouse: transfer.toWarehouseName,
            status: transfer.status,
            reason: transferItem?.reason,
            requestedBy: transfer.requestedBy,
            approvedBy: transfer.approvedBy,
            transferDate: transfer.date,
            completedDate: transfer.completedDate,
            notes: transfer.notes,
            urgency: transfer.urgency,
            attachments: transfer.attachmentName
          }
        });

        // إضافة حدث للاستلام في المخزن الجديد إذا كان مكتملاً
        if (transfer.status === 'completed' && transfer.completedDate) {
          events.push({
            icon: <PackageCheck className="h-5 w-5" />,
            title: 'استلام في المخزن الجديد',
            description: `تم استلام الجهاز في مخزن '${transfer.toWarehouseName}' بنجاح وأصبح متاحاً للاستخدام.`,
            date: transfer.completedDate,
            color: 'bg-green-500/20 text-green-400',
            user: transfer.receivedBy || 'موظف المخزن',
            details: {
              transferNumber: transfer.transferNumber,
              finalWarehouse: transfer.toWarehouseName,
              receivedBy: transfer.receivedBy,
              completedDate: transfer.completedDate,
              originalTransferDate: transfer.date
            }
          });
        }
      }
    });

    // 6.5. Maintenance Receipt Events - إيصالات استلام/تسليم الصيانة (جديد)
    (maintenanceReceipts as any[]).forEach((receipt: any) => {
      const receiptItems = Array.isArray(receipt.items) ? receipt.items :
                          (typeof receipt.items === 'string' ? JSON.parse(receipt.items) : []);
      
      const deviceItem = receiptItems.find((item: any) => item.deviceId === searchedImei);
      if (deviceItem) {
        const receiptType = receipt.receiptNumber.includes('REC') ? 'استلام من الصيانة' : 'تسليم للصيانة';
        
        events.push({
          icon: <FileText className="h-5 w-5" />,
          title: receiptType,
          description: `${receiptType} ضمن إيصال ${receipt.receiptNumber}. ${receipt.notes ? `ملاحظات: ${receipt.notes}` : ''}`,
          date: receipt.date,
          color: receiptType.includes('استلام') ? 'bg-green-500/20 text-green-400' : 'bg-blue-500/20 text-blue-400',
          user: receipt.employeeName,
          details: {
            receiptNumber: receipt.receiptNumber,
            referenceNumber: receipt.referenceNumber,
            receiptType: receiptType,
            employeeName: receipt.employeeName,
            maintenanceEmployeeName: receipt.maintenanceEmployeeName,
            notes: receipt.notes,
            status: receipt.status,
            attachments: receipt.attachmentName,
            items: receiptItems
          }
        });
      }
    });

    // 6.7. Delivery Order Events - أوامر التسليم (جديد)
    (deliveryOrders as any[]).forEach((order: any) => {
      const orderItems = Array.isArray(order.items) ? order.items : [];
      
      const deviceInOrder = orderItems.find((item: any) => item.deviceId === searchedImei);
      if (deviceInOrder) {
        events.push({
          icon: <Shuffle className="h-5 w-5" />,
          title: 'أمر تسليم',
          description: `تم إنشاء أمر تسليم رقم ${order.deliveryOrderNumber} للجهاز. ${order.notes ? `ملاحظات: ${order.notes}` : ''}`,
          date: order.date,
          color: 'bg-indigo-500/20 text-indigo-400',
          user: order.employeeName,
          details: {
            deliveryOrderNumber: order.deliveryOrderNumber,
            referenceNumber: order.referenceNumber,
            warehouseId: order.warehouseId,
            warehouseName: order.warehouseName,
            employeeName: order.employeeName,
            notes: order.notes,
            status: order.status,
            attachments: order.attachmentName
          }
        });
      }
    });

    // 7. Sale Event - بيع الجهاز (محسن)
    const sale = (sales as any[]).find((s: any) =>
      (Array.isArray(s.items) && s.items.some((item: any) => item.deviceId === searchedImei))
    );
    if (sale) {
      const saleItem = sale.items.find((item: any) => item.deviceId === searchedImei);
      const warrantyText = sale.warrantyPeriod === '3d' ? '3 أيام' :
                          sale.warrantyPeriod === '1w' ? 'أسبوع' :
                          sale.warrantyPeriod === '1m' ? 'شهر' :
                          sale.warrantyPeriod === '3m' ? '3 أشهر' :
                          sale.warrantyPeriod === '6m' ? '6 أشهر' :
                          sale.warrantyPeriod === '1y' ? 'سنة' : 'بدون ضمان';

      events.push({
        icon: <ShoppingCart className="h-5 w-5" />,
        title: 'بيع الجهاز',
        description: `تم بيع الجهاز للعميل '${sale.clientName}' ضمن فاتورة ${sale.soNumber}${sale.opNumber ? ` (الفاتورة الرسمية: ${sale.opNumber})` : ''}. ${saleItem?.salePrice ? `سعر البيع: ${saleItem.salePrice}` : ''} - الضمان: ${warrantyText}.`,
        date: sale.date,
        color: 'bg-green-500/20 text-green-400',
        user: sale.employeeName || 'قسم المبيعات',
        details: {
          soNumber: sale.soNumber,
          opNumber: sale.opNumber,
          clientName: sale.clientName,
          clientPhone: sale.clientPhone,
          clientAddress: sale.clientAddress,
          salePrice: saleItem?.salePrice,
          cost: saleItem?.cost,
          profit: saleItem?.salePrice && saleItem?.cost ? (saleItem.salePrice - saleItem.cost) : null,
          warrantyPeriod: sale.warrantyPeriod,
          warrantyText: warrantyText,
          paymentMethod: sale.paymentMethod,
          discount: sale.discount,
          salesEmployee: sale.employeeName,
          warehouseName: sale.warehouseName,
          notes: sale.notes,
          attachments: sale.attachmentName
        }
      });
    }

    // 8. Return and Replacement Events - المرتجعات والاستبدال (محسن)
    (returns as any[]).forEach((returnOrder: any) => {
      const returnedItem = Array.isArray(returnOrder.items) ? returnOrder.items.find(
        (item: any) => item.deviceId === searchedImei
      ) : null;
      const replacementItem = Array.isArray(returnOrder.items) ? returnOrder.items.find(
        (item: any) => item.replacementDeviceId === searchedImei
      ) : null;

      if (returnedItem) {
        // تحديد لون الإرجاع بناءً على السبب
        const returnColor = returnedItem.returnReason?.includes('عطل') ? 'bg-red-500/20 text-red-400' :
                           returnedItem.returnReason?.includes('تالف') ? 'bg-orange-500/20 text-orange-400' :
                           returnedItem.returnReason?.includes('عيب') ? 'bg-yellow-500/20 text-yellow-400' :
                           'bg-red-500/20 text-red-400';

        // حساب المدة بين البيع والإرجاع
        const originalSale = (sales as any[]).find((s: any) =>
          s.soNumber === returnOrder.originalSaleInfo?.soNumber
        );
        const daysBetween = originalSale ? 
          Math.floor((new Date(returnOrder.date).getTime() - new Date(originalSale.date).getTime()) / (1000 * 60 * 60 * 24)) : null;

        events.push({
          icon: <Undo2 className="h-5 w-5" />,
          title: 'إرجاع الجهاز',
          description: `تم إرجاع هذا الجهاز من العميل '${returnOrder.clientName}' في أمر المرتجع رقم ${returnOrder.roNumber}. سبب الإرجاع: ${returnedItem.returnReason || 'غير محدد'}${daysBetween ? ` بعد ${daysBetween} يوم من البيع` : ''}.${returnedItem.replacementDeviceId ? ` تم تقديم جهاز بديل برقم ${returnedItem.replacementDeviceId}.` : ''}`,
          date: returnOrder.date,
          color: returnColor,
          user: returnOrder.employeeName || 'قسم المرتجعات',
          details: {
            returnOrderNumber: returnOrder.roNumber,
            clientName: returnOrder.clientName,
            clientPhone: returnOrder.clientPhone,
            returnReason: returnedItem.returnReason,
            returnType: returnedItem.returnType,
            returnDate: returnOrder.date,
            deviceModel: returnedItem.model,
            originalSaleInfo: returnOrder.originalSaleInfo,
            originalSaleDate: originalSale?.date,
            daysBetweenSaleAndReturn: daysBetween,
            hasReplacement: !!returnedItem.replacementDeviceId,
            replacementDeviceId: returnedItem.replacementDeviceId,
            refundAmount: returnedItem.refundAmount,
            restockingFee: returnedItem.restockingFee,
            condition: returnedItem.condition,
            notes: returnOrder.notes,
            processedBy: returnOrder.employeeName,
            warrantyStatus: returnedItem.warrantyStatus
          }
        });
      }

      if (replacementItem) {
        events.push({
          icon: <Replace className="h-5 w-5" />,
          title: 'جهاز بديل',
          description: `تم صرف هذا الجهاز كبديل للعميل '${returnOrder.clientName}' في أمر المرتجع رقم ${returnOrder.roNumber} عن الجهاز الأصلي (${replacementItem.deviceId}). سبب الاستبدال: ${replacementItem.returnReason || 'غير محدد'}. ${replacementItem.additionalCost ? `تكلفة إضافية: ${replacementItem.additionalCost}` : 'بدون تكلفة إضافية'}.`,
          date: returnOrder.date,
          color: 'bg-blue-500/20 text-blue-400',
          user: returnOrder.employeeName || 'قسم المرتجعات',
          details: {
            returnOrderNumber: returnOrder.roNumber,
            clientName: returnOrder.clientName,
            originalDeviceId: replacementItem.deviceId,
            originalDeviceModel: replacementItem.model,
            replacementReason: replacementItem.returnReason,
            replacementDate: returnOrder.date,
            isReplacementDevice: true,
            originalSaleInfo: returnOrder.originalSaleInfo,
            additionalCost: replacementItem.additionalCost,
            newWarrantyPeriod: replacementItem.newWarrantyPeriod,
            conditionDifference: replacementItem.conditionDifference,
            approvedBy: returnOrder.approvedBy,
            notes: returnOrder.notes
          }
        });
      }
    });

    // إضافة أي أحداث إضافية للجهاز قيد التتبع
    if (device) {
      // أحداث الحالة الحالية للجهاز
      if (device.status && device.lastUpdated) {
        const statusEvents = {
          'متاح للبيع': {
            icon: <CheckCircle className="h-5 w-5" />,
            title: 'الجهاز متاح للبيع',
            description: `الجهاز حالياً في المخزن ومتاح للبيع. الموقع: ${device.location || 'غير محدد'}`,
            color: 'bg-green-500/20 text-green-400'
          },
          'قيد الصيانة': {
            icon: <Wrench className="h-5 w-5" />,
            title: 'الجهاز قيد الصيانة',
            description: `الجهاز حالياً في قسم الصيانة. الموقع: ${device.location || 'غير محدد'}`,
            color: 'bg-orange-500/20 text-orange-400'
          },
          'مُباع': {
            icon: <ShoppingCart className="h-5 w-5" />,
            title: 'الجهاز مُباع',
            description: `الجهاز تم بيعه ولم يعد متاحاً في المخزن. آخر موقع: ${device.location || 'غير محدد'}`,
            color: 'bg-blue-500/20 text-blue-400'
          }
        };

        const statusInfo = statusEvents[device.status as keyof typeof statusEvents];
        if (statusInfo) {
          events.push({
            icon: statusInfo.icon,
            title: statusInfo.title,
            description: statusInfo.description,
            date: device.lastUpdated || new Date().toISOString(),
            color: statusInfo.color,
            user: 'النظام',
            details: {
              currentStatus: device.status,
              currentLocation: device.location,
              model: device.model,
              lastUpdated: device.lastUpdated,
              supplier: device.supplier
            }
          });
        }
      }
    }

    console.log('📊 Total events aggregated:', events.length);
    console.log('📋 Event types summary:', events.reduce((acc: any, event) => {
      const type = event.title.split(' ')[0];
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {}));

    return events
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .map((event, index) => ({
        ...event,
        id: `${event.date}-${event.title}-${index}`,
        formattedDate: formatDateTime(event.date, { arabic: true }),
      }));
  }, [
    searchedImei,
    devices,
    sales,
    returns,
    supplyOrders,
    suppliers,
    evaluationOrders,
    maintenanceHistory,
    maintenanceOrders,
    maintenanceReceiptOrders,
    maintenanceLogs,
    warehouseTransfers,
    deliveryOrders
  ]);

  // تحديث الأحداث المفلترة عند تغيير الأحداث الكاملة
  React.useEffect(() => {
    setFilteredTimelineEvents(fullTimelineEvents);
  }, [fullTimelineEvents]);

  const customerViewDetails = useMemo(() => {
    if (!searchedImei) return null;

    const device = (devices as any[]).find((d: any) => d.id === searchedImei);
    if (!device) return null;

    const lastSale = (sales as any[])
      .filter((s: any) => (Array.isArray(s.items) && s.items.some((item: any) => item.deviceId === searchedImei)))
      .sort(
        (a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime(),
      )[0];

    let warrantyInfo = {
      status: 'لا يوجد بيع مسجل',
      expiryDate: '',
      remaining: '',
    };

    if (lastSale) {
      const saleDate = new Date(lastSale.date);
      let expiryDate: Date | null = null;

      switch (lastSale.warrantyPeriod) {
        case '3d':
          expiryDate = addDays(saleDate, 3);
          break;
        case '1w':
          expiryDate = addDays(saleDate, 7);
          break;
        case '1m':
          expiryDate = addMonths(saleDate, 1);
          break;
        case '3m':
          expiryDate = addMonths(saleDate, 3);
          break;
        case '6m':
          expiryDate = addMonths(saleDate, 6);
          break;
        case '1y':
          expiryDate = addYears(saleDate, 1);
          break;
        default:
          break;
      }

      if (expiryDate) {
        const today = new Date();
        if (isAfter(expiryDate, today)) {
          warrantyInfo.status = 'في الضمان';
          warrantyInfo.remaining = formatDistanceToNowStrict(expiryDate, {
            locale: ar,
            addSuffix: true,
          });
        } else {
          warrantyInfo.status = 'ضمان منتهي';
        }
        warrantyInfo.expiryDate = format(expiryDate, 'yyyy-MM-dd');
      } else {
        warrantyInfo.status = 'بدون ضمان';
      }
    }

    const replacementInfo = (returns as any[]).find((r: any) =>
      (Array.isArray(r.items) && r.items.some((item: any) => item.replacementDeviceId === searchedImei))
    );
    const originalItem = replacementInfo?.items.find(
      (item: any) => item.replacementDeviceId === searchedImei
    );

    return {
      device,
      lastSale,
      warrantyInfo,
      originalItemInfo: originalItem
        ? {
            ...originalItem,
            returnDate: replacementInfo.date,
          }
        : null,
    };
  }, [searchedImei, devices, sales, returns, suppliers, supplyOrders, evaluationOrders, maintenanceHistory, maintenanceLogs, maintenanceOrders, maintenanceReceipts, deliveryOrders, warehouseTransfers]);

  // دالة معالجة تغيير الفلاتر
  const handleFilterChange = (filtered: TimelineEvent[]) => {
    setFilteredTimelineEvents(filtered);
  };

  const device = (devices as any[]).find((d: any) => d.id === searchedImei);

  const handlePrint = async (action: 'print' | 'download') => {
    if (!searchedImei || !device) return;

    // تحضير البيانات للطباعة
    const deviceData = {
      model: device.model,
      id: searchedImei,
      status: device.status,
      lastSale: isCustomerView && customerViewDetails?.lastSale ? {
        clientName: customerViewDetails.lastSale.clientName,
        soNumber: customerViewDetails.lastSale.soNumber,
        opNumber: customerViewDetails.lastSale.opNumber,
        date: customerViewDetails.lastSale.date
      } : undefined,
      warrantyInfo: isCustomerView && customerViewDetails?.warrantyInfo ? {
        status: customerViewDetails.warrantyInfo.status,
        expiryDate: customerViewDetails.warrantyInfo.expiryDate,
        remaining: customerViewDetails.warrantyInfo.remaining
      } : undefined
    };

    try {
      // استخدام الدالة المحدثة للطباعة
      await printDeviceTrackingReport(deviceData, fullTimelineEvents.map(event => ({
        id: event.id || `${event.date}-${event.title}`,
        type: event.title.includes('بيع') ? 'بيع' :
              event.title.includes('إرجاع') ? 'إرجاع' :
              event.title.includes('صيانة') ? 'صيانة' :
              event.title.includes('تقييم') ? 'تقييم' :
              event.title.includes('توريد') ? 'توريد' : 'عام',
        title: event.title,
        description: event.description,
        date: event.date,
        user: event.user,
        details: event.details
      })), {
        language: 'both',
        isCustomerView,
        action,
        filename: `${isCustomerView ? 'customer_' : ''}device_report_${searchedImei}.pdf`,
        useCanvasMethod // استخدام الخيار المحدد من المستخدم
      });
    } catch (error) {
      console.error('Error printing device report:', error);

      // العودة للطريقة القديمة في حالة الخطأ
      if (action === 'print') {
        const elementId = isCustomerView ? 'customer-view-container' : 'timeline-container';
        const title = isCustomerView
          ? `تقرير تتبع الجهاز (نسخة العميل)`
          : `سجل تاريخ الجهاز - ${device.model} (${searchedImei})`;

        printElementWithSettings(elementId, title, {
          language: 'both',
          isCustomerView,
          action
        });
      }
    }

  };

  return (
    <div className="track-page flex flex-col gap-6" dir="rtl">
      <Card className="search-card border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 fade-in">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            🔍 تتبع الأجهزة المتقدم
          </CardTitle>
          <CardDescription className="text-lg text-gray-600 dark:text-gray-300">
            أدخل الرقم التسلسلي (IMEI) لعرض سجل تاريخ الجهاز المفصل مع جميع العمليات
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="search-container slide-in-right">
            <div className="flex w-full max-w-3xl mx-auto items-center space-x-3 space-x-reverse">
              <div className="relative flex-1">
                <Input
                  type="text"
                  placeholder="أدخل IMEI أو امسح الباركود للبحث المتقدم..."
                  className="text-right pl-12 pr-4 py-4 text-lg border-2 border-blue-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-200/50 rounded-xl shadow-sm transition-all duration-300 hover:shadow-md glow-effect focus-ring"
                  value={imei}
                  onChange={(e) => setImei(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Barcode className="absolute left-3 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400 transition-colors duration-300" />
              </div>
              <Button
                onClick={handleSearch}
                className="px-10 py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 interactive-element"
                disabled={!imei.trim()}
              >
                <Barcode className="ml-2 h-5 w-5" />
                بحث متقدم
              </Button>
            </div>
          </div>

          <div className="flex justify-center slide-in-left">
            <div className="flex flex-col gap-4">
              {/* خيار نسخة العميل */}
              <div className="flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-white to-blue-50 dark:from-gray-800 dark:to-gray-700 px-6 py-3 rounded-full shadow-lg border border-blue-200 dark:border-gray-600 hover:shadow-xl transition-all duration-300 interactive-element">
                <Checkbox
                  id="customer-view"
                  checked={isCustomerView}
                  onCheckedChange={(checked) => setIsCustomerView(!!checked)}
                  className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 transition-all duration-300"
                />
                <Label htmlFor="customer-view" className="cursor-pointer font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <User className="h-5 w-5 text-blue-600" />
                  <span className="text-lg">نسخة العميل المبسطة</span>
                </Label>
              </div>
              
              {/* خيار طريقة التصدير */}
              <div className="flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-white to-green-50 dark:from-gray-800 dark:to-gray-700 px-6 py-3 rounded-full shadow-lg border border-green-200 dark:border-gray-600 hover:shadow-xl transition-all duration-300 interactive-element">
                <Checkbox
                  id="canvas-method"
                  checked={useCanvasMethod}
                  onCheckedChange={(checked) => setUseCanvasMethod(!!checked)}
                  className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600 transition-all duration-300"
                />
                <Label htmlFor="canvas-method" className="cursor-pointer font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <FileText className="h-5 w-5 text-green-600" />
                  <span className="text-lg">استخدام Canvas (للمشاكل الفنية)</span>
                </Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {searchedImei && !device && (
        <Card className="border-red-200 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20 fade-in">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Barcode className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-xl text-red-800 dark:text-red-200">لم يتم العثور على الجهاز</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-red-600 dark:text-red-300 text-lg mb-4">
              الرقم التسلسلي <span className="font-mono bg-red-100 px-2 py-1 rounded">{searchedImei}</span> غير موجود في النظام
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              تأكد من صحة الرقم التسلسلي أو تواصل مع الإدارة للمساعدة
            </p>
          </CardContent>
        </Card>
      )}

      {searchedImei && device && (
        <div className="space-y-6">
          {/* قسم تفاصيل الجهاز الأساسية */}
          <DeviceDetailsSection
            device={device}
            searchedImei={searchedImei}
            customerViewDetails={customerViewDetails}
            evaluationOrders={evaluationOrders}
            supplyOrders={supplyOrders}
            suppliers={suppliers}
            warehouseTransfers={warehouseTransfers}
          />

          {/* إحصائيات متقدمة للجهاز */}
          <DeviceAdvancedStats 
            events={fullTimelineEvents}
            deviceData={device}
          />

          {isCustomerView ? (
            customerViewDetails && (
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => setShowReportPreview(true)}>
                    <FileText className="ml-2 h-4 w-4" /> معاينة وطباعة التقرير
                  </Button>
                  <Button variant="outline" onClick={() => handlePrint('print')}>
                    <Printer className="ml-2 h-4 w-4" /> طباعة {useCanvasMethod ? '(Canvas)' : '(HTML)'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePrint('download')}
                  >
                    <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
                  </Button>
                </div>
              <div id="customer-view-container" className="customer-view-enhanced grid grid-cols-1 md:grid-cols-2 gap-6 animate-in fade-in-50 print-section">
                <Card className="enhanced-card card-sale md:col-span-2">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <FileText className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold text-gray-800">تفاصيل البيع</CardTitle>
                        <CardDescription className="text-gray-600 rtl-container">
                          آخر عملية بيع مسجلة لهذا الجهاز
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {customerViewDetails.lastSale ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-green-500">
                          <div className="flex items-center gap-2 mb-2">
                            <User className="h-4 w-4 text-green-600" />
                            <span className="font-semibold text-gray-700">العميل</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {customerViewDetails.lastSale.clientName}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-blue-500">
                          <div className="flex items-center gap-2 mb-2">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <span className="font-semibold text-gray-700">فاتورة البيع</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {customerViewDetails.lastSale.soNumber}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-purple-500">
                          <div className="flex items-center gap-2 mb-2">
                            <FileText className="h-4 w-4 text-purple-600" />
                            <span className="font-semibold text-gray-700">الفاتورة الرسمية</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {customerViewDetails.lastSale.opNumber || 'لا يوجد'}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-orange-500">
                          <div className="flex items-center gap-2 mb-2">
                            <ShoppingCart className="h-4 w-4 text-orange-600" />
                            <span className="font-semibold text-gray-700">تاريخ البيع</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {formatDateTime(customerViewDetails.lastSale.date, { arabic: true })}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 text-lg">
                          لا توجد عملية بيع مسجلة لهذا الجهاز
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                <Card className={`enhanced-card card-warranty ${
                  customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'warranty-active' :
                  customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'warranty-expired' : 'warranty-none'
                }`}>
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'bg-green-100' :
                        customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'bg-red-100' : 'bg-gray-100'
                      }`}>
                        <ShieldCheck className={`h-6 w-6 ${
                          customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'text-green-600' :
                          customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'text-red-600' : 'text-gray-600'
                        }`} />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold text-gray-800">حالة الضمان</CardTitle>
                        <CardDescription className="text-gray-600">
                          معلومات الضمان الحالية للجهاز
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className={`p-4 rounded-lg text-center ${
                      customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'bg-green-50 border-2 border-green-200' :
                      customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'bg-red-50 border-2 border-red-200' : 'bg-gray-50 border-2 border-gray-200'
                    }`}>
                      <div className={`text-2xl font-bold mb-2 ${
                        customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'text-green-700' :
                        customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'text-red-700' : 'text-gray-700'
                      }`}>
                        {customerViewDetails.warrantyInfo.status}
                      </div>
                      {customerViewDetails.warrantyInfo.remaining && (
                        <div className="text-sm text-gray-600">
                          <strong>الوقت المتبقي:</strong> {customerViewDetails.warrantyInfo.remaining}
                        </div>
                      )}
                    </div>
                    {customerViewDetails.warrantyInfo.expiryDate && (
                      <div className="bg-gray-50 p-3 rounded-lg border-r-4 border-blue-500">
                        <div className="flex items-center gap-2 mb-1">
                          <ShieldCheck className="h-4 w-4 text-blue-600" />
                          <span className="font-semibold text-gray-700">تاريخ انتهاء الضمان</span>
                        </div>
                        <p className="text-gray-800 font-medium">
                          {customerViewDetails.warrantyInfo.expiryDate}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                {customerViewDetails.originalItemInfo && (
                  <Card className="enhanced-card card-replacement md:col-span-2">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <Replace className="h-6 w-6 text-purple-600" />
                        </div>
                        <div>
                          <CardTitle className="text-xl font-bold text-gray-800">معلومات الاستبدال</CardTitle>
                          <CardDescription className="text-gray-600">
                            تفاصيل عملية استبدال الجهاز
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="bg-purple-50 p-4 rounded-lg border-2 border-purple-200 text-center">
                        <Replace className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                        <p className="text-purple-800 font-medium text-lg">
                          هذا الجهاز تم تسليمه كبديل لجهاز آخر
                        </p>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-purple-500">
                          <div className="flex items-center gap-2 mb-2">
                            <Package className="h-4 w-4 text-purple-600" />
                            <span className="font-semibold text-gray-700">الجهاز الأصلي</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {customerViewDetails.originalItemInfo.model}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-indigo-500">
                          <div className="flex items-center gap-2 mb-2">
                            <Barcode className="h-4 w-4 text-indigo-600" />
                            <span className="font-semibold text-gray-700">الرقم التسلسلي الأصلي</span>
                          </div>
                          <p className="text-gray-800 font-medium text-sm">
                            {customerViewDetails.originalItemInfo.deviceId}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-orange-500">
                          <div className="flex items-center gap-2 mb-2">
                            <Undo2 className="h-4 w-4 text-orange-600" />
                            <span className="font-semibold text-gray-700">تاريخ الإرجاع</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {formatDateTime(customerViewDetails.originalItemInfo.returnDate, { arabic: true })}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )
        ) : (
          <div className="space-y-4">
            {/* أزرار الطباعة والتصدير */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowReportPreview(true)}
                title="معاينة وطباعة التقرير"
              >
                <FileText className="h-4 w-4 ml-1" />
                معاينة التقرير
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePrint('print')}
                title={`طباعة ${useCanvasMethod ? '(Canvas)' : '(HTML محسن)'}`}
              >
                <Printer className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePrint('download')}
                title="تصدير PDF"
              >
                <FileDown className="h-4 w-4" />
              </Button>
            </div>

            {/* نظام الفلاتر المتقدم */}
            <DeviceTrackingFilters
              events={fullTimelineEvents}
              onFilterChange={handleFilterChange}
              deviceInfo={{
                model: device.model,
                id: searchedImei,
                status: device.status
              }}
            />

            {/* السجل التاريخي المحسن */}
            <DeviceHistoryTimeline
              events={filteredTimelineEvents.map(event => ({
                ...event,
                type: event.title.includes('توريد') ? 'supply' :
                      event.title.includes('فحص') ? 'evaluation' :
                      event.title.includes('صيانة') ? 'maintenance' :
                      event.title.includes('تحويل') ? 'transfer' :
                      event.title.includes('بيع') ? 'sale' :
                      event.title.includes('إرجاع') ? 'return' :
                      event.title.includes('بديل') ? 'replacement' : 'default'
              }))}
              searchedImei={searchedImei}
              deviceModel={device.model}
            />

            {/* عرض العمليات المفصل */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg font-bold text-right">
                  تفاصيل العمليات المفصلة
                </CardTitle>
                <CardDescription className="text-right">
                  عرض شامل لجميع العمليات مع التفاصيل الكاملة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 device-tracking-enhanced">
                  {filteredTimelineEvents.map((event, index) => (
                    <DeviceOperationDetails
                      key={index}
                      operation={{
                        id: event.id || `${index}`,
                        type: event.title.includes('توريد') ? 'supply' :
                              event.title.includes('فحص') ? 'evaluation' :
                              event.title.includes('صيانة') ? 'maintenance' :
                              event.title.includes('تحويل') ? 'transfer' :
                              event.title.includes('بيع') ? 'sale' :
                              event.title.includes('إرجاع') ? 'return' :
                              event.title.includes('بديل') ? 'replacement' : 'default',
                        operation: event.title,
                        date: event.date,
                        user: event.user || 'غير محدد',
                        department: device.location || 'غير محدد',
                        details: event.details || {},
                        status: event.description.includes('مكتمل') ? 'مكتمل' :
                               event.description.includes('معلق') ? 'معلق' :
                               event.description.includes('قيد التنفيذ') ? 'قيد التنفيذ' : 'مكتمل',
                        location: device.location,
                        reference: event.id,
                        notes: event.description
                      }}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
        </div>
      )}

      {/* مكون معاينة التقرير */}
      {searchedImei && device && (
        <ReportPreview
          isOpen={showReportPreview}
          onClose={() => setShowReportPreview(false)}
          deviceData={{
            model: device.model,
            id: searchedImei,
            status: device.status,
            lastSale: isCustomerView && customerViewDetails?.lastSale ? {
              clientName: customerViewDetails.lastSale.clientName,
              soNumber: customerViewDetails.lastSale.soNumber,
              opNumber: customerViewDetails.lastSale.opNumber,
              date: customerViewDetails.lastSale.date
            } : undefined,
            warrantyInfo: isCustomerView && customerViewDetails?.warrantyInfo ? {
              status: customerViewDetails.warrantyInfo.status,
              expiryDate: customerViewDetails.warrantyInfo.expiryDate || '',
              remaining: customerViewDetails.warrantyInfo.remaining || ''
            } : undefined
          }}
          timelineEvents={filteredTimelineEvents.map(event => ({
            id: `${event.date}-${event.title}`,
            type: event.title.includes('بيع') ? 'بيع' :
                  event.title.includes('إرجاع') ? 'إرجاع' :
                  event.title.includes('صيانة') ? 'صيانة' :
                  event.title.includes('تقييم') ? 'تقييم' :
                  event.title.includes('توريد') ? 'توريد' : 'عام',
            title: event.title,
            description: event.description,
            date: event.date,
            user: event.user
          }))}
        />
      )}
    </div>
  );
}